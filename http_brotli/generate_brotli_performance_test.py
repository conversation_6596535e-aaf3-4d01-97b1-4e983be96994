#!/usr/bin/env python3
"""
HTTP Brotli解压性能测试PCAP生成器
专门用于生成Brotli解压缩性能测试的PCAP文件
参考PostgreSQL性能测试的实现方式
"""

from scapy.all import *
from scapy.layers.inet import IP, TCP
from scapy.all import wrpcap
import struct
import random
import os
import time
import brotli
import json
import uuid

# HTTP 默认端口
HTTP_PORT = 80

# 客户端和服务器IP地址池 - 增加四元组丰富性
CLIENT_IPS = [
    "************", "************", "************", "************",
    "************", "************", "************", "************",
    "************", "************", "************", "************",
    "************", "************", "************", "************"
]

SERVER_IPS = [
    "************", "************", "************", "************",
    "************", "************", "************", "************"
]

# 输出目录
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "http_brotli_pcaps")

def create_http_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个HTTP数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_http_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_http_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_http_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_http_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_http_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_http_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_http_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def create_http_request(method, path, headers=None, body=b""):
    """创建HTTP请求"""
    if headers is None:
        headers = {}
    
    # 构建请求行
    request_line = f"{method} {path} HTTP/1.1\r\n"
    
    # 构建头部
    header_lines = []
    for key, value in headers.items():
        header_lines.append(f"{key}: {value}\r\n")
    
    # 组装完整请求
    request = request_line.encode() + b"".join(h.encode() for h in header_lines) + b"\r\n" + body
    return request

def create_http_response(status_code, status_text, headers=None, body=b""):
    """创建HTTP响应"""
    if headers is None:
        headers = {}
    
    # 构建状态行
    status_line = f"HTTP/1.1 {status_code} {status_text}\r\n"
    
    # 构建头部
    header_lines = []
    for key, value in headers.items():
        header_lines.append(f"{key}: {value}\r\n")
    
    # 组装完整响应
    response = status_line.encode() + b"".join(h.encode() for h in header_lines) + b"\r\n" + body
    return response

def generate_test_data_1000bytes(request_id, target_request_size=500, target_response_size=500):
    """生成约1000字节的HTTP请求-响应对数据（请求500+响应500）

    Args:
        request_id: 请求ID
        target_request_size: 目标请求包大小（包括TCP/IP头部）
        target_response_size: 目标响应包大小（包括TCP/IP头部）

    Returns:
        包含请求和响应数据的字典
    """
    # TCP/IP头部开销约54字节
    tcp_ip_overhead = 54

    # 生成HTTP请求，控制到约500字节
    request_headers = {
        "Host": "perf.example.com",
        "User-Agent": "BrotliPerfTest/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br",
        "X-Request-Id": str(request_id),
        "Connection": "close"
    }

    # 计算请求路径和填充，使请求包达到目标大小
    base_path = f"/api/perf/{request_id}"
    base_request = create_http_request("GET", base_path, request_headers)
    base_request_size = len(base_request) + tcp_ip_overhead

    # 如果需要填充请求到500字节
    if base_request_size < target_request_size:
        padding_needed = target_request_size - base_request_size - 20  # 留20字节余量
        if padding_needed > 0:
            # 通过添加查询参数来填充请求
            padding_params = []
            param_count = 0
            while len("&".join(padding_params)) < padding_needed:
                param_count += 1
                param_value = f"perf_test_param_{param_count}_" + "x" * 20
                padding_params.append(f"p{param_count}={param_value}")
                if param_count > 20:  # 防止无限循环
                    break

            padded_path = base_path + "?" + "&".join(padding_params)[:padding_needed]
            request = create_http_request("GET", padded_path, request_headers)
        else:
            request = base_request
    else:
        request = base_request

    request_packet_size = len(request) + tcp_ip_overhead

    # 生成响应数据，控制压缩后大小到约500字节
    # 计算响应可用空间
    available_for_response = target_response_size - tcp_ip_overhead

    # 估算HTTP响应头部大小
    response_headers_template = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": "999",  # 占位符
        "Server": "BrotliPerfServer/1.0",
        "X-Request-Id": str(request_id),
        "Connection": "close"
    }

    # 计算响应头部大小（不包括Content-Length的实际值）
    temp_response = create_http_response(200, "OK", response_headers_template, b"")
    response_headers_size = len(temp_response) - len("999")  # 减去占位符长度

    # 计算压缩数据的目标大小
    target_compressed_size = available_for_response - response_headers_size - 10  # 留10字节余量
    target_compressed_size = max(50, target_compressed_size)  # 最小50字节

    # 生成原始JSON数据并压缩到目标大小
    base_data = {
        "request_id": request_id,
        "timestamp": "2024-01-01T12:00:00Z",
        "status": "success",
        "data": {
            "user_id": request_id,
            "name": f"TestUser{request_id:06d}",
            "email": f"user{request_id:06d}@perf-test.com",
            "profile": {
                "age": 25 + (request_id % 50),
                "city": f"TestCity{request_id % 100}",
                "department": f"Department{request_id % 20}"
            }
        },
        "metadata": {
            "compression": "brotli",
            "test_type": "performance",
            "target_size": target_response_size
        }
    }

    # 通过调整填充数据来控制压缩后大小
    # 使用二分查找找到合适的原始数据大小
    min_size = 200
    max_size = 3000
    best_compressed_size = 0
    best_original_data = None

    for _ in range(25):  # 最多尝试25次
        current_size = (min_size + max_size) // 2

        # 生成当前大小的数据
        test_data = base_data.copy()
        padding_needed = current_size - len(json.dumps(test_data, separators=(',', ':')))

        if padding_needed > 0:
            # 添加填充数据
            padding_text = "performance_test_padding_data_" * (padding_needed // 30 + 1)
            test_data["padding"] = padding_text[:padding_needed]

        # 转换为JSON并压缩
        json_data = json.dumps(test_data, separators=(',', ':')).encode('utf-8')
        compressed_data = brotli.compress(json_data, quality=6)

        compressed_size = len(compressed_data)

        # 检查是否接近目标大小
        if abs(compressed_size - target_compressed_size) < abs(best_compressed_size - target_compressed_size):
            best_compressed_size = compressed_size
            best_original_data = json_data

        # 调整搜索范围
        if compressed_size < target_compressed_size:
            min_size = current_size + 1
        else:
            max_size = current_size - 1

        # 如果足够接近目标，就停止
        if abs(compressed_size - target_compressed_size) <= 3:
            break

    # 使用最佳结果
    final_compressed = brotli.compress(best_original_data, quality=6)

    # 生成最终的HTTP响应
    final_response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(final_compressed)),
        "Server": "BrotliPerfServer/1.0",
        "X-Request-Id": str(request_id),
        "Connection": "close"
    }

    response = create_http_response(200, "OK", final_response_headers, final_compressed)
    response_packet_size = len(response) + tcp_ip_overhead

    total_size = request_packet_size + response_packet_size

    return {
        'request': request,
        'response': response,
        'request_packet_size': request_packet_size,
        'response_packet_size': response_packet_size,
        'total_size': total_size,
        'original_data': best_original_data,
        'compressed_data': final_compressed,
        'compression_ratio': len(final_compressed) / len(best_original_data),
        'original_size': len(best_original_data),
        'compressed_size': len(final_compressed)
    }

def generate_brotli_performance_pcap_1000bytes(target_requests=50000, output_dir="http_brotli_pcaps"):
    """生成约1000字节的Brotli解压性能测试PCAP文件

    参考PostgreSQL性能测试，每个请求-响应对约1000字节（请求500+响应500）
    """

    print("=" * 80)
    print("HTTP Brotli 解压性能测试 PCAP 生成器 (1000字节版本)")
    print("=" * 80)
    print(f"目标请求数: {target_requests:,}")
    print(f"目标包大小: ~1000字节/请求-响应对 (请求500+响应500)")
    print(f"参考标准: PostgreSQL性能测试")

    # 计算参数
    packets_needed = target_requests * 2  # 每个请求包含请求+响应
    target_pair_size = 1000  # 目标请求-响应对大小
    estimated_size_mb = packets_needed * target_pair_size / 1024 / 1024

    # 计算会话数量
    max_sessions = len(CLIENT_IPS) * len(SERVER_IPS) * 100  # 每个IP对最多100个端口
    sessions_needed = min(target_requests // 20, max_sessions)  # 每个会话处理20个请求

    print(f"需要生成报文数: {packets_needed:,}")
    print(f"预计文件大小: {estimated_size_mb:.1f} MB")
    print(f"客户端IP数量: {len(CLIENT_IPS)}")
    print(f"服务器IP数量: {len(SERVER_IPS)}")
    print(f"预计会话数: {sessions_needed:,}")
    print("-" * 80)
    
    # 确认继续
    response = input("是否继续生成? (y/N): ").lower()
    if response != 'y':
        print("已取消生成")
        return

    start_time = time.time()
    packets = []

    print("开始生成测试数据...")

    # 测试1000字节数据生成
    print("测试1000字节数据包生成...")
    test_data = generate_test_data_1000bytes(1)
    print(f"  请求包大小: {test_data['request_packet_size']} 字节")
    print(f"  响应包大小: {test_data['response_packet_size']} 字节")
    print(f"  总大小: {test_data['total_size']} 字节")
    print(f"  压缩率: {test_data['compression_ratio']:.2%}")

    print("创建会话池...")

    # 创建会话池
    sessions = []
    for i in range(sessions_needed):
        client_ip = CLIENT_IPS[i % len(CLIENT_IPS)]
        server_ip = SERVER_IPS[i % len(SERVER_IPS)]
        client_port = 40000 + (i % 20000)  # 端口范围40000-59999

        sessions.append({
            'client_ip': client_ip,
            'server_ip': server_ip,
            'client_port': client_port,
            'seq_num': random.randint(1000000, 9000000),
            'ack_num': random.randint(1000000, 9000000),
            'handshake_done': False
        })

    print(f"创建了 {len(sessions)} 个会话")

    # 建立TCP连接
    print("建立TCP连接...")
    for i, session in enumerate(sessions):
        if i % 1000 == 0 and i > 0:
            print(f"已建立 {i} 个连接...")

        handshake_packets, session['seq_num'], session['ack_num'] = create_tcp_handshake(
            session['client_ip'], session['server_ip'], session['client_port'], HTTP_PORT)
        packets.extend(handshake_packets)
        session['handshake_done'] = True

    print(f"所有 {len(sessions)} 个TCP连接已建立")

    # 生成请求-响应对
    print("生成HTTP请求-响应对...")
    request_count = 0

    # 统计信息
    total_request_size = 0
    total_response_size = 0
    total_pair_size = 0

    for i in range(target_requests):
        request_count += 1
        session_idx = (request_count - 1) % len(sessions)
        session = sessions[session_idx]

        # 生成1000字节的请求-响应对数据
        test_data = generate_test_data_1000bytes(request_count)

        # 发送请求包
        c_packet = create_http_packet(session['client_ip'], session['server_ip'],
                                    session['client_port'], HTTP_PORT,
                                    session['seq_num'], session['ack_num'], test_data['request'])
        packets.append(c_packet)
        session['seq_num'] += len(test_data['request'])

        # 发送响应包
        s_packet = create_http_packet(session['server_ip'], session['client_ip'],
                                    HTTP_PORT, session['client_port'],
                                    session['ack_num'], session['seq_num'], test_data['response'])
        packets.append(s_packet)
        session['ack_num'] += len(test_data['response'])

        # 累计统计信息
        total_request_size += test_data['request_packet_size']
        total_response_size += test_data['response_packet_size']
        total_pair_size += test_data['total_size']

        # 进度显示
        if request_count % 5000 == 0:
            progress = (request_count / target_requests) * 100
            elapsed = time.time() - start_time
            avg_pair_size = total_pair_size / request_count
            print(f"进度: {progress:.1f}% ({request_count:,}/{target_requests:,}) - 已用时: {elapsed:.1f}s - 平均包大小: {avg_pair_size:.0f}字节")
    
    # 关闭TCP连接
    print("关闭TCP连接...")
    for i, session in enumerate(sessions):
        if i % 1000 == 0 and i > 0:
            print(f"已关闭 {i} 个连接...")
        
        teardown_packets = create_tcp_teardown(session['client_ip'], session['server_ip'], 
                                             session['client_port'], HTTP_PORT, 
                                             session['seq_num'], session['ack_num'])
        packets.extend(teardown_packets)
    
    generation_time = time.time() - start_time
    print(f"数据包生成完成! 用时: {generation_time:.1f}s")
    print(f"实际生成报文数: {len(packets):,}")
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 写入PCAP文件
    filename = f"http_brotli_performance_{target_requests//1000}k_requests_500bytes.pcap"
    filepath = os.path.join(output_dir, filename)

    print(f"正在写入文件: {filepath}")
    write_start = time.time()
    wrpcap(filepath, packets)
    write_time = time.time() - write_start

    # 获取实际文件大小
    file_size = os.path.getsize(filepath)
    file_size_mb = file_size / 1024 / 1024

    # 计算平均包大小
    avg_request_size = total_request_size / request_count
    avg_response_size = total_response_size / request_count
    avg_pair_size = total_pair_size / request_count

    print("=" * 80)
    print("Brotli性能测试PCAP生成完成! (1000字节版本)")
    print("=" * 80)
    print(f"文件路径: {filepath}")
    print(f"文件大小: {file_size_mb:.1f} MB")
    print(f"报文数量: {len(packets):,}")
    print(f"请求数量: {request_count:,}")
    print(f"会话数量: {len(sessions):,}")
    print(f"生成用时: {generation_time:.1f}s")
    print(f"写入用时: {write_time:.1f}s")
    print(f"总用时: {generation_time + write_time:.1f}s")
    print()

    # 包大小统计
    print("包大小统计 (符合PostgreSQL性能测试标准):")
    print(f"  平均请求包大小: {avg_request_size:.0f} 字节")
    print(f"  平均响应包大小: {avg_response_size:.0f} 字节")
    print(f"  平均请求-响应对大小: {avg_pair_size:.0f} 字节")
    print(f"  目标大小: 1000 字节 (请求500+响应500)")
    print(f"  大小偏差: {avg_pair_size - 1000:+.0f} 字节 ({(avg_pair_size - 1000)/1000*100:+.1f}%)")

    # tcpreplay建议
    recommended_mbps = int(file_size_mb * 8 * 1.1)  # 加10%余量
    print()
    print("=" * 80)
    print("tcpreplay 使用建议:")
    print("=" * 80)
    print(f"# 基础命令")
    print(f"tcpreplay -i eth0 -M {recommended_mbps} {filepath}")
    print()
    print(f"# 高精度回放 (推荐)")
    print(f"tcpreplay -i eth0 -M {recommended_mbps} --preload-pcap {filepath}")
    print()
    print(f"预期QPS: {target_requests:,}")
    print(f"建议带宽: {recommended_mbps} Mbps")
    print(f"每秒数据量: {avg_pair_size * target_requests / 1024 / 1024:.1f} MB/s")
    print()
    print("性能测试重点:")
    print("- Brotli解压缩性能和延迟")
    print("- 内存使用情况")
    print("- CPU占用率")
    print("- 1000字节包处理效率 (请求500+响应500)")
    print("负载均衡效果: 多IP多端口，可触发TCP负载均衡机制")

def calculate_qps_requirements_1000bytes(target_qps=100000):
    """计算指定QPS的压测需求 (基于1000字节包大小)"""
    print("=" * 80)
    print(f"HTTP Brotli {target_qps//1000}万QPS 压测需求计算 (1000字节版本)")
    print("=" * 80)

    # 使用1000字节的包大小进行计算
    print("基于1000字节请求-响应对的计算:")

    # 测试1000字节数据包
    test_data = generate_test_data_1000bytes(1)
    avg_pair_size = test_data['total_size']

    print(f"  请求包大小: {test_data['request_packet_size']} 字节")
    print(f"  响应包大小: {test_data['response_packet_size']} 字节")
    print(f"  总包大小: {avg_pair_size} 字节")

    # QPS需求计算
    packets_per_second = target_qps * 2  # 每个QPS包含1个请求+1个响应
    bytes_per_second = target_qps * avg_pair_size
    mbps_needed = (bytes_per_second * 8) / (1024 * 1024)  # 转换为Mbps

    print("\n" + "=" * 80)
    print(f"{target_qps//1000}万QPS 压测需求:")
    print("=" * 80)
    print(f"每秒数据包数量: {packets_per_second:,} 个 ({target_qps:,}请求 + {target_qps:,}响应)")
    print(f"每秒数据量: {bytes_per_second/1024/1024:.1f} MB")
    print(f"理论最小带宽: {mbps_needed:.0f} Mbps")
    print(f"建议带宽(+20%): {mbps_needed*1.2:.0f} Mbps")
    print(f"建议带宽(+50%): {mbps_needed*1.5:.0f} Mbps")

    # PCAP文件大小估算
    pcap_file_size_mb = bytes_per_second / 1024 / 1024
    print(f"\n1秒钟PCAP文件大小: {pcap_file_size_mb:.1f} MB")

    durations = [1, 5, 10, 30, 60]
    print("\n不同测试时长的PCAP文件大小:")
    for duration in durations:
        file_size = pcap_file_size_mb * duration
        print(f"  {duration:2d} 秒: {file_size:6.1f} MB")

    print("\n" + "=" * 80)
    print("tcpreplay 使用建议:")
    print("=" * 80)
    print(f"# 精确速率控制")
    print(f"tcpreplay -i eth0 -M {mbps_needed:.0f} your_file.pcap")
    print(f"\n# 推荐使用(+20%余量)")
    print(f"tcpreplay -i eth0 -M {mbps_needed*1.2:.0f} your_file.pcap")
    print(f"\n# 高负载测试(+50%余量)")
    print(f"tcpreplay -i eth0 -M {mbps_needed*1.5:.0f} your_file.pcap")

    return {
        'target_qps': target_qps,
        'packets_per_second': packets_per_second,
        'bytes_per_second': bytes_per_second,
        'mbps_needed': mbps_needed,
        'recommended_mbps': mbps_needed * 1.2,
        'high_load_mbps': mbps_needed * 1.5,
        'pcap_size_mb_per_second': pcap_file_size_mb
    }

def main():
    """主函数"""
    print("HTTP Brotli 解压性能测试 PCAP 生成器")
    print("专门用于测试Brotli解压缩性能")
    print()

    # 显示菜单
    print("请选择操作:")
    print("1. 计算QPS压测需求")
    print("2. 生成性能测试PCAP文件")
    print("3. 退出")

    try:
        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == "1":
            # 计算QPS需求
            qps_input = input("请输入目标QPS (默认100000): ").strip()
            if qps_input:
                target_qps = int(qps_input)
            else:
                target_qps = 100000

            calculate_qps_requirements_1000bytes(target_qps)

        elif choice == "2":
            # 生成PCAP文件
            requests_input = input("请输入目标请求数 (默认50000): ").strip()
            if requests_input:
                target_requests = int(requests_input)
            else:
                target_requests = 50000

            if target_requests <= 0:
                print("请求数必须大于0")
                return

            print(f"将生成 {target_requests:,} 个请求，每个请求-响应对约1000字节 (请求500+响应500)")

            # 生成PCAP文件
            generate_brotli_performance_pcap_1000bytes(target_requests)

        elif choice == "3":
            print("退出程序")
            return

        else:
            print("无效选择")
            return

    except ValueError:
        print("无效输入")
        return

if __name__ == "__main__":
    main()
