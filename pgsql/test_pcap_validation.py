#!/usr/bin/env python3
"""
PostgreSQL PCAP文件验证脚本
用于验证生成的PCAP文件中PostgreSQL协议消息的格式正确性
"""

import struct
from scapy.all import *

def validate_postgresql_message(data, offset=0):
    """验证PostgreSQL协议消息格式"""
    if len(data) < offset + 4:
        return False, "数据太短，无法包含完整的PostgreSQL消息头"

    # 检查是否是启动消息（没有消息类型字节，直接以长度开始）
    # 启动消息的长度通常比较大，且第5-8字节是协议版本号
    if offset == 0 and len(data) >= 8:
        potential_length = struct.unpack("!I", data[offset:offset+4])[0]
        potential_version = struct.unpack("!I", data[offset+4:offset+8])[0]

        # 检查是否是启动消息（协议版本号通常是196608）
        if potential_version == 196608 and potential_length > 8 and potential_length < 1000:
            if len(data) >= potential_length:
                return True, f"启动消息: 长度 {potential_length} - 格式正确"
            else:
                return False, f"启动消息: 声明长度 {potential_length}，但实际数据不足"

    # 普通PostgreSQL消息（有消息类型字节）
    if len(data) < offset + 5:
        return False, "数据太短，无法包含完整的PostgreSQL消息头"

    # 读取消息类型和长度
    msg_type = chr(data[offset])
    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]

    # 验证长度字段
    expected_total_length = offset + 1 + msg_length
    if len(data) < expected_total_length:
        return False, f"消息类型 '{msg_type}': 声明长度 {msg_length}，但实际数据不足"

    return True, f"消息类型 '{msg_type}': 长度 {msg_length} - 格式正确"

def validate_pcap_file(filename):
    """验证PCAP文件中的PostgreSQL消息"""
    print(f"\n=== 验证文件: {filename} ===")
    
    try:
        packets = rdpcap(filename)
        pgsql_packets = 0
        valid_messages = 0
        invalid_messages = 0
        
        for packet in packets:
            if packet.haslayer(TCP) and packet[TCP].dport == 5432 or packet[TCP].sport == 5432:
                if hasattr(packet[TCP], 'payload') and len(packet[TCP].payload) > 0:
                    pgsql_packets += 1
                    payload = bytes(packet[TCP].payload)
                    
                    # 跳过TCP握手等非PostgreSQL数据
                    if len(payload) < 5:
                        continue
                        
                    # 验证PostgreSQL消息
                    offset = 0
                    while offset < len(payload):
                        if offset + 4 > len(payload):
                            break

                        is_valid, message = validate_postgresql_message(payload, offset)
                        if is_valid:
                            valid_messages += 1
                            print(f"  ✓ {message}")

                            # 移动到下一个消息
                            if "启动消息" in message:
                                # 启动消息：直接使用长度字段
                                msg_length = struct.unpack("!I", payload[offset:offset+4])[0]
                                offset += msg_length
                            else:
                                # 普通消息：消息类型(1) + 长度字段包含的内容
                                if offset + 5 > len(payload):
                                    break
                                msg_length = struct.unpack("!I", payload[offset+1:offset+5])[0]
                                offset += 1 + msg_length
                        else:
                            invalid_messages += 1
                            print(f"  ✗ {message}")
                            break
        
        print(f"\n总结:")
        print(f"  PostgreSQL数据包: {pgsql_packets}")
        print(f"  有效消息: {valid_messages}")
        print(f"  无效消息: {invalid_messages}")
        
        return invalid_messages == 0
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    import sys
    print("PostgreSQL PCAP文件格式验证工具")
    print("=" * 50)

    # 如果有命令行参数，使用指定的文件；否则使用默认文件列表
    if len(sys.argv) > 1:
        test_files = sys.argv[1:]
    else:
        # 测试中优先级场景生成的文件
        test_files = [
            "pgsql_pcaps/pgsql_multiple_data_types.pcap",
            "pgsql_pcaps/pgsql_character_encoding.pcap",
            "pgsql_pcaps/pgsql_pipelined_queries.pcap",
            "pgsql_pcaps/pgsql_function_call.pcap"
        ]
    
    all_valid = True
    for filename in test_files:
        try:
            is_valid = validate_pcap_file(filename)
            if not is_valid:
                all_valid = False
        except FileNotFoundError:
            print(f"\n文件不存在: {filename}")
            all_valid = False
        except Exception as e:
            print(f"\n验证 {filename} 时出错: {e}")
            all_valid = False
    
    print("\n" + "=" * 50)
    if all_valid:
        print("✓ 所有测试文件的PostgreSQL协议格式都正确！")
    else:
        print("✗ 发现协议格式问题，需要进一步修复。")

if __name__ == "__main__":
    main()
